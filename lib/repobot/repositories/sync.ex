defmodule Repobot.Repositories.Sync do
  @moduledoc """
  Handles synchronization of files between repositories, either through direct updates or pull requests.
  """

  @behaviour Repobot.Repositories.Sync.Behaviour

  require Logger

  alias Repobot.{Repository, SourceFile, SourceFiles}

  @doc """
  Synchronizes multiple source files to a target repository in a batch,
  either creating a single PR with all changes or pushing all changes directly.
  This preserves the commit structure from the template repository.

  Returns:
  - `{:ok, url}` where url is the PR URL if sync_mode is :pr
  - `{:ok, "Files updated successfully"}` if sync_mode is :direct
  - `{:error, reason}` if synchronization fails
  """
  @impl true
  def sync_changes(
        source_files,
        %Repository{} = template_repo,
        %Repository{} = target_repo,
        github_client,
        opts \\ []
      )
      when is_list(source_files) do
    mode = Keyword.get(opts, :mode, template_repo.sync_mode)

    Logger.info(
      "Syncing #{length(source_files)} files from #{template_repo.full_name} to #{target_repo.full_name} in #{mode} mode"
    )

    # First ensure we only process unique source files to avoid duplicate PRs
    unique_source_files =
      source_files
      |> Enum.uniq_by(& &1.id)

    Logger.info("Processing #{length(unique_source_files)} unique files after deduplication")

    # For each source file, render the content and get target file state
    files_with_content =
      Enum.reduce_while(unique_source_files, {:ok, []}, fn source_file, {:ok, acc} ->
        Logger.info(
          "Processing source file #{source_file.name} (is_template: #{source_file.is_template})"
        )

        with {:ok, rendered_content} <-
               SourceFiles.render_template_for_repository(source_file, target_repo) do
          # Get file state and handle both success and error cases
          case get_file_state(github_client, target_repo, source_file, rendered_content) do
            {:error, reason} ->
              # Pass the error up to be handled
              {:halt, {:error, reason}}

            file_state ->
              # Process the file state as before
              if file_state.needs_update do
                {:cont, {:ok, [file_state | acc]}}
              else
                Logger.info("No changes needed for #{source_file.name} - content is identical")
                {:cont, {:ok, acc}}
              end
          end
        else
          error -> {:halt, error}
        end
      end)

    case files_with_content do
      {:ok, []} ->
        {:ok, "No changes needed - all files are identical"}

      {:ok, files_to_update} ->
        case mode do
          :pr ->
            create_pull_request_for_multiple_files(
              github_client,
              files_to_update,
              target_repo,
              template_repo,
              opts
            )

          :direct ->
            update_files_directly(github_client, files_to_update, target_repo, opts)
        end

      error ->
        error
    end
  end

  # Helper to get file state including content, sha, and whether it needs update
  defp get_file_state(github_client, target_repo, source_file, rendered_content) do
    Logger.info("Checking file state for #{source_file.name} in #{target_repo.full_name}")

    case github_api().get_file_content(
           github_client,
           target_repo.owner,
           target_repo.name,
           source_file.target_path
         ) do
      {:ok, current_content, %{"sha" => sha}} ->
        Logger.info("File #{source_file.name} exists in target repository")
        needs_update = current_content != rendered_content

        if needs_update do
          Logger.info("File #{source_file.name} content differs - will be updated")
        else
          Logger.info("File #{source_file.name} content is identical - no update needed")
        end

        %{
          source_file: source_file,
          content: rendered_content,
          sha: sha,
          exists: true,
          needs_update: needs_update
        }

      # These are expected cases where we need to create the file
      {:error, 404} ->
        Logger.info("File #{source_file.name} not found in target repo (404) - will be created")

        %{
          source_file: source_file,
          content: rendered_content,
          sha: nil,
          exists: false,
          needs_update: true
        }

      {:error, :file_not_found} ->
        Logger.info(
          "File #{source_file.name} not found in target repo (:file_not_found) - will be created"
        )

        %{
          source_file: source_file,
          content: rendered_content,
          sha: nil,
          exists: false,
          needs_update: true
        }

      {:error, reason} ->
        # Only actual errors should be returned as error tuples
        Logger.error("Error getting file state for #{source_file.name}: #{inspect(reason)}")
        {:error, "Failed to get file state: #{inspect(reason)}"}
    end
  end

  # Update multiple files with direct commits to the default branch
  defp update_files_directly(github_client, files, target_repo, opts) do
    Logger.info(
      "Directly updating #{length(files)} files in #{target_repo.full_name} with single commit"
    )

    # Use the original commit message if provided
    commit_message = Keyword.get(opts, :commit_message)
    message = if commit_message, do: commit_message, else: "Update multiple files via Repobot"

    # For commits with multiple files, we need to create a tree with all changes
    Logger.info("Creating batch commit for #{length(files)} files with message: #{message}")

    # First get the default branch reference and its SHA
    case get_default_branch_ref(github_client, target_repo) do
      {:ok, {default_branch, base_tree_sha, base_commit_sha}} ->
        # Create a tree with all the file changes
        tree_entries =
          Enum.map(files, fn file ->
            path = file.source_file.target_path
            # Using variable directly without assigning

            # Determine mode based on file extension
            mode = get_file_mode(path)

            # Create tree entry for this file
            %{
              "path" => path,
              "mode" => mode,
              "type" => "blob",
              "content" => file.content
            }
          end)

        Logger.debug("Creating tree with entries: #{inspect(tree_entries)}")

        # Create a new tree
        case github_api().create_tree(
               github_client,
               target_repo.owner,
               target_repo.name,
               tree_entries,
               base_tree_sha
             ) do
          {201, %{"sha" => new_tree_sha}, _} ->
            Logger.info("Created new tree with SHA: #{new_tree_sha}")

            # Create a commit with the new tree
            case github_api().create_commit(
                   github_client,
                   target_repo.owner,
                   target_repo.name,
                   message,
                   new_tree_sha,
                   [base_commit_sha]
                 ) do
              {201, %{"sha" => new_commit_sha}, _} ->
                Logger.info("Created new commit with SHA: #{new_commit_sha}")

                # Update the reference to point to the new commit
                case github_api().update_reference(
                       github_client,
                       target_repo.owner,
                       target_repo.name,
                       "heads/#{default_branch}",
                       new_commit_sha,
                       false
                     ) do
                  {200, _response, _} ->
                    Logger.info("Successfully updated reference to new commit")
                    {:ok, "Files updated successfully in a single commit"}

                  {status, body, _} ->
                    error_msg = "Failed to update reference: #{status} - #{inspect(body)}"
                    Logger.error(error_msg)
                    {:error, error_msg}
                end

              {status, body, _} ->
                error_msg = "Failed to create commit: #{status} - #{inspect(body)}"
                Logger.error(error_msg)
                {:error, error_msg}
            end

          {status, body, _} ->
            error_msg = "Failed to create tree: #{status} - #{inspect(body)}"
            Logger.error(error_msg)
            {:error, error_msg}
        end

      {:error, reason} ->
        Logger.error("Failed to get default branch information: #{reason}")
        {:error, reason}
    end
  end

  # Get file mode based on extension - executable (100755) or normal file (100644)
  defp get_file_mode(path) do
    executable_extensions = [".sh", ".bash", ".py", ".rb"]

    if Enum.any?(executable_extensions, &String.ends_with?(path, &1)) do
      # Executable
      "100755"
    else
      # Regular file
      "100644"
    end
  end

  # Get default branch reference and SHAs needed for creating a commit
  defp get_default_branch_ref(github_client, repo) do
    case github_api().get_repo(github_client, repo.owner, repo.name) do
      {200, repo_info, _} ->
        default_branch = repo_info["default_branch"]

        case github_api().get_ref(
               github_client,
               repo.owner,
               repo.name,
               "heads/#{default_branch}"
             ) do
          {200, %{"object" => %{"sha" => base_commit_sha}}, _} ->
            # We need the tree SHA associated with this commit for creating a new tree
            case github_api().get_commit(
                   github_client,
                   repo.owner,
                   repo.name,
                   base_commit_sha
                 ) do
              {200, %{"tree" => %{"sha" => base_tree_sha}}, _} ->
                {:ok, {default_branch, base_tree_sha, base_commit_sha}}

              {status, body, _} ->
                {:error, "Failed to get base commit tree SHA: #{status} - #{inspect(body)}"}
            end

          {status, body, _} ->
            {:error, "Failed to get default branch ref SHA: #{status} - #{inspect(body)}"}
        end

      {status, body, _} ->
        {:error, "Failed to get repository info: #{status} - #{inspect(body)}"}
    end
  end

  # Create a single PR with all changes
  defp create_pull_request_for_multiple_files(
         github_client,
         files,
         target_repo,
         _template_repo,
         opts
       ) do
    # Ensure we only process unique files by source file ID
    unique_files = Enum.uniq_by(files, fn file -> file.source_file.id end)

    if length(unique_files) < length(files) do
      Logger.info(
        "Deduplicated #{length(files) - length(unique_files)} files to prevent duplicate PRs"
      )
    end

    file_ids = Enum.map(unique_files, fn file -> file.source_file.id end)

    Logger.info(
      "Creating PR for #{length(unique_files)} unique files in #{target_repo.full_name} with a single commit"
    )

    Logger.info("File IDs being processed: #{inspect(file_ids)}")

    with {:ok, {default_branch, _base_tree_sha, base_commit_sha}} <-
           get_default_branch_ref(github_client, target_repo),
         # Create a unique branch name
         branch_name = "update-multiple-files-#{DateTime.utc_now() |> DateTime.to_unix()}",
         # Create the new branch pointing to the base commit
         {:ok, ref} <- create_branch(github_client, target_repo, branch_name, default_branch) do
      # Get the original commit message if provided, otherwise use a default
      commit_message =
        Keyword.get(opts, :commit_message, "Update multiple files via Repobot")

      # Create tree entries for all file changes
      tree_entries =
        Enum.map(unique_files, fn file ->
          path = file.source_file.target_path
          mode = get_file_mode(path)

          %{
            "path" => path,
            "mode" => mode,
            "type" => "blob",
            # Content is already rendered
            "content" => file.content
          }
        end)

      Logger.debug("Creating tree with entries: #{inspect(tree_entries)}")

      # Create a new tree based on the default branch's base commit tree
      # NOTE: We use base_commit_sha here as the base for the *commit*,
      # the create_tree call needs the SHA of the *tree* associated with that base commit.
      # Fetch the base commit to get its tree SHA.
      case github_api().get_commit(
             github_client,
             target_repo.owner,
             target_repo.name,
             base_commit_sha
           ) do
        {200, %{"tree" => %{"sha" => base_tree_sha_for_commit}}, _} ->
          # Create the new tree using the correct base tree SHA
          case github_api().create_tree(
                 github_client,
                 target_repo.owner,
                 target_repo.name,
                 tree_entries,
                 # Use the tree SHA from the base commit
                 base_tree_sha_for_commit
               ) do
            {201, %{"sha" => new_tree_sha}, _} ->
              Logger.info("Created new tree with SHA: #{new_tree_sha}")

              # Create a single commit with the new tree, using the default branch head as parent
              case github_api().create_commit(
                     github_client,
                     target_repo.owner,
                     target_repo.name,
                     commit_message,
                     new_tree_sha,
                     # Parent commit is the head of the default branch
                     [base_commit_sha]
                   ) do
                {201, %{"sha" => new_commit_sha}, _} ->
                  Logger.info("Created single commit for all files with SHA: #{new_commit_sha}")

                  # Update the new branch reference to point to the new commit
                  case github_api().update_reference(
                         github_client,
                         target_repo.owner,
                         target_repo.name,
                         # ref includes refs/heads/ prefix
                         String.replace_prefix(ref, "refs/", ""),
                         new_commit_sha,
                         # Don't force update
                         false
                       ) do
                    {200, _response, _} ->
                      Logger.info(
                        "Successfully updated branch #{branch_name} to point to commit #{new_commit_sha}"
                      )

                      # Create the PR from the updated branch
                      create_multi_file_pr(
                        github_client,
                        target_repo,
                        branch_name,
                        default_branch,
                        unique_files,
                        # Pass opts to reuse commit message for PR title/body
                        opts
                      )

                    {status, body, _} ->
                      error_msg =
                        "Failed to update branch reference #{branch_name}: #{status} - #{inspect(body)}"

                      Logger.error(error_msg)
                      {:error, error_msg}
                  end

                {status, body, _} ->
                  error_msg =
                    "Failed to create commit on branch #{branch_name}: #{status} - #{inspect(body)}"

                  Logger.error(error_msg)
                  {:error, error_msg}
              end

            {status, body, _} ->
              error_msg =
                "Failed to create tree on branch #{branch_name}: #{status} - #{inspect(body)}"

              Logger.error(error_msg)
              {:error, error_msg}
          end

        {status, body, _} ->
          error_msg =
            "Failed to get base commit tree SHA (#{base_commit_sha}): #{status} - #{inspect(body)}"

          Logger.error(error_msg)
          {:error, error_msg}
      end
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # Create PR for multiple files, optionally using the original commit message
  defp create_multi_file_pr(github_client, repo, branch_name, default_branch, files, opts) do
    title =
      case Keyword.get(opts, :commit_message) do
        nil ->
          "Update multiple managed files"

        message ->
          # Use first line of commit message as PR title
          message
          |> String.split("\n", parts: 2)
          |> List.first()
          |> String.slice(0..60)
          |> Kernel.<>("...")
      end

    body = """
    This PR was automatically created by Repobot to update the following files:

    #{Enum.map_join(files, "\n", fn file -> "- #{file.source_file.name}" end)}

    These files are managed centrally and synchronized across repositories.
    """

    # Add original commit message to PR description if provided
    body =
      case Keyword.get(opts, :commit_message) do
        nil ->
          body

        message ->
          body <> "\n\n## Original Commit Message\n\n```\n#{message}\n```"
      end

    case github_api().create_pull_request(
           github_client,
           repo.owner,
           repo.name,
           title,
           body,
           branch_name,
           default_branch
         ) do
      {201, response, _} -> {:ok, response["html_url"]}
      {status, body, _} -> {:error, "Failed to create PR: #{status} - #{inspect(body)}"}
    end
  end

  defp create_file_directly(
         github_client,
         source_file,
         target_repo,
         content,
         commit_message
       ) do
    Logger.info("Creating file in #{target_repo.full_name}")

    message =
      if commit_message, do: commit_message, else: "Create #{source_file.name} via Repobot"

    encoded_content = Base.encode64(content)

    case github_api().create_file(
           github_client,
           target_repo.owner,
           target_repo.name,
           source_file.target_path,
           encoded_content,
           message,
           nil
         ) do
      {201, _response, _} -> {:ok, "File created successfully"}
      {status, body, _} -> {:error, "Failed to create file: #{status} - #{inspect(body)}"}
    end
  end

  defp update_file_directly(
         github_client,
         source_file,
         target_repo,
         content,
         sha,
         commit_message \\ nil
       ) do
    Logger.info("Directly updating file in #{target_repo.full_name}")

    message =
      if commit_message, do: commit_message, else: "Update #{source_file.name} via Repobot"

    encoded_content = Base.encode64(content)

    case github_api().update_file(
           github_client,
           target_repo.owner,
           target_repo.name,
           source_file.target_path,
           encoded_content,
           message,
           sha,
           nil
         ) do
      {200, _response, _} -> {:ok, "File updated successfully"}
      {status, body, _} -> {:error, "Failed to update file: #{status} - #{inspect(body)}"}
    end
  end

  defp create_branch_content(
         github_client,
         source_file,
         repo,
         content,
         branch_name,
         commit_message
       ) do
    message =
      if commit_message, do: commit_message, else: "Create #{source_file.name} via Repobot"

    encoded_content = Base.encode64(content)

    case github_api().create_file(
           github_client,
           repo.owner,
           repo.name,
           source_file.target_path,
           encoded_content,
           message,
           branch_name
         ) do
      {201, response, _} -> {:ok, response}
      {status, body, _} -> {:error, "Failed to create file: #{status} - #{inspect(body)}"}
    end
  end

  defp update_branch_content(
         github_client,
         source_file,
         repo,
         content,
         sha,
         branch_name,
         commit_message
       ) do
    message =
      if commit_message, do: commit_message, else: "Update #{source_file.name} via Repobot"

    encoded_content = Base.encode64(content)

    case github_api().update_file(
           github_client,
           repo.owner,
           repo.name,
           source_file.target_path,
           encoded_content,
           message,
           sha,
           branch_name
         ) do
      {200, response, _} -> {:ok, response}
      {status, body, _} -> {:error, "Failed to update file: #{status} - #{inspect(body)}"}
    end
  end

  defp get_default_branch(github_client, repo) do
    case github_api().get_repo(github_client, repo.owner, repo.name) do
      {200, repo_info, _} -> {:ok, repo_info["default_branch"]}
      {status, body, _} -> {:error, "Failed to get repository info: #{status} - #{inspect(body)}"}
    end
  end

  defp create_branch(github_client, repo, branch_name, default_branch) do
    case github_api().get_ref(github_client, repo.owner, repo.name, "heads/#{default_branch}") do
      {200, %{"object" => %{"sha" => sha}}, _} ->
        ref = "refs/heads/#{branch_name}"

        case github_api().create_ref(github_client, repo.owner, repo.name, ref, sha) do
          {201, _response, _} -> {:ok, ref}
          {status, body, _} -> {:error, "Failed to create branch: #{status} - #{inspect(body)}"}
        end

      {status, body, _} ->
        {:error, "Failed to get default branch ref: #{status} - #{inspect(body)}"}
    end
  end

  @doc """
  Synchronizes a source file to a target repository, either through a direct update or pull request
  based on the template repository's sync_mode setting, or defaults to :direct if no template repository.

  Returns:
  - `{:ok, url}` where url is the PR URL if sync_mode is :pr
  - `{:ok, "File updated successfully"}` if sync_mode is :direct
  - `{:ok, "No changes needed - content is identical"}` if content is the same
  - `{:error, reason}` if synchronization fails
  """
  @impl true
  def sync_file(
        %SourceFile{} = source_file,
        template_repo,
        %Repository{} = target_repo,
        github_client,
        opts \\ []
      ) do
    source_description =
      if template_repo, do: "from #{template_repo.full_name}", else: "without source repository"

    Logger.info("Syncing #{source_file.name} #{source_description} to #{target_repo.full_name}")

    Logger.info(
      "Processing source file #{source_file.name} (is_template: #{source_file.is_template})"
    )

    with {:ok, rendered_content} <-
           SourceFiles.render_template_for_repository(source_file, target_repo) do
      case github_api().get_file_content(
             github_client,
             target_repo.owner,
             target_repo.name,
             source_file.target_path
           ) do
        {:ok, current_content, %{"sha" => sha}} ->
          if current_content != rendered_content do
            # Default to :direct mode if no template repository
            default_mode = if template_repo, do: template_repo.sync_mode, else: :direct

            case Keyword.get(opts, :mode, default_mode) do
              :pr ->
                create_pull_request(
                  github_client,
                  source_file,
                  target_repo,
                  rendered_content,
                  sha
                )

              :direct ->
                update_file_directly(
                  github_client,
                  source_file,
                  target_repo,
                  rendered_content,
                  sha
                )
            end
          else
            {:ok, "No changes needed - content is identical"}
          end

        {:error, 404} ->
          # File doesn't exist yet, create it
          # Default to :direct mode if no template repository
          default_mode = if template_repo, do: template_repo.sync_mode, else: :direct

          case Keyword.get(opts, :mode, default_mode) do
            :pr ->
              create_pull_request(github_client, source_file, target_repo, rendered_content, nil)

            :direct ->
              create_file_directly(github_client, source_file, target_repo, rendered_content, nil)
          end

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  @doc """
  Installs a file in a target repository when we know it doesn't exist yet.
  Respects the sync_mode setting of the template repository, or defaults to :direct if no template repository.

  Returns:
  - `{:ok, url}` where url is the PR URL if sync_mode is :pr
  - `{:ok, "File created successfully"}` if sync_mode is :direct
  - `{:error, reason}` if installation fails
  """
  def install_file(
        %SourceFile{} = source_file,
        template_repo,
        %Repository{} = target_repo,
        github_client,
        opts \\ []
      ) do
    source_description =
      if template_repo, do: "from #{template_repo.full_name}", else: "without source repository"

    Logger.info(
      "Installing #{source_file.name} #{source_description} to #{target_repo.full_name}"
    )

    Logger.info(
      "Processing source file #{source_file.name} (is_template: #{source_file.is_template})"
    )

    with {:ok, rendered_content} <-
           SourceFiles.render_template_for_repository(source_file, target_repo) do
      # Default to :direct mode if no template repository
      default_mode = if template_repo, do: template_repo.sync_mode, else: :direct

      case Keyword.get(opts, :mode, default_mode) do
        :pr ->
          create_pull_request(github_client, source_file, target_repo, rendered_content, nil)

        :direct ->
          create_file_directly(github_client, source_file, target_repo, rendered_content, nil)
      end
    end
  end

  defp create_pull_request(github_client, source_file, target_repo, content, sha) do
    Logger.info("Creating PR for #{target_repo.full_name}")

    with {:ok, default_branch} <- get_default_branch(github_client, target_repo),
         branch_name = "update-#{source_file.name}-#{DateTime.utc_now() |> DateTime.to_unix()}",
         {:ok, _ref} <- create_branch(github_client, target_repo, branch_name, default_branch),
         {:ok, _content} <-
           (if sha do
              update_branch_content(
                github_client,
                source_file,
                target_repo,
                content,
                sha,
                branch_name,
                nil
              )
            else
              create_branch_content(
                github_client,
                source_file,
                target_repo,
                content,
                branch_name,
                nil
              )
            end),
         {:ok, pr} <-
           create_pr(github_client, target_repo, branch_name, default_branch, source_file) do
      {:ok, pr["html_url"]}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp create_pr(github_client, repo, branch_name, default_branch, source_file) do
    title = "Update #{source_file.name}"

    body = """
    This PR was automatically created by Repobot to update `#{source_file.name}`.

    The file content is managed centrally and synchronized across repositories.
    """

    case github_api().create_pull_request(
           github_client,
           repo.owner,
           repo.name,
           title,
           body,
           branch_name,
           default_branch
         ) do
      {201, response, _} -> {:ok, response}
      {status, body, _} -> {:error, "Failed to create PR: #{status} - #{inspect(body)}"}
    end
  end

  defp github_api do
    Application.get_env(:repobot, :github_api)
  end

  # Gets a GitHub client for a repository
  defp get_github_client(%Repository{owner: owner, name: name}) do
    try do
      client = github_api().client(owner, name)
      {:ok, client}
    rescue
      e ->
        Logger.error("Failed to get GitHub client: #{Exception.message(e)}")
        {:error, "Failed to get GitHub client: #{Exception.message(e)}"}
    end
  end

  @doc """
  Updates files in the target repo by directly committing to the default branch.
  """
  def update_files_directly(
        file_updates,
        target_repo,
        _template_repo,
        _commit_sha,
        commit_message
      ) do
    Logger.info(
      "Syncing to target repo #{target_repo.full_name} with #{length(file_updates)} files in a single commit"
    )

    case get_github_client(target_repo) do
      {:ok, client} ->
        update_files_in_batch(client, file_updates, target_repo, commit_message)

      {:error, reason} ->
        Logger.error(
          "Failed to get GitHub client for target repo #{target_repo.full_name}: #{inspect(reason)}"
        )

        {:error, :github_client_error}
    end
  end

  @doc """
  Updates multiple files in a single commit using the Git Data API.
  """
  def update_files_in_batch(client, file_updates, target_repo, commit_message) do
    github_api = github_api()
    owner = target_repo.owner
    repo = target_repo.name

    # Get the default branch reference
    Logger.info("Getting default branch reference for #{owner}/#{repo}")

    case get_default_branch_ref(github_api, client, owner, repo) do
      {:ok, ref, ref_sha} ->
        Logger.info("Got default branch reference. Current HEAD is at #{ref_sha}")

        # Create a tree with all file changes
        tree_entries =
          Enum.map(file_updates, fn {path, content, _} ->
            Logger.debug("Adding tree entry for #{path}")

            %{
              "path" => path,
              # file mode
              "mode" => "100644",
              "type" => "blob",
              "content" => content
            }
          end)

        Logger.info("Creating tree with #{length(tree_entries)} entries")

        case github_api.create_tree(client, owner, repo, tree_entries, ref_sha) do
          {201, tree_data, _} ->
            tree_sha = tree_data["sha"]
            Logger.info("Created tree with SHA: #{tree_sha}")

            # Create a commit with the new tree
            Logger.info("Creating commit with message: #{commit_message}")

            case github_api.create_commit(client, owner, repo, commit_message, tree_sha, [ref_sha]) do
              {201, commit_data, _} ->
                commit_sha = commit_data["sha"]
                Logger.info("Created commit with SHA: #{commit_sha}")

                # Update the reference to point to the new commit
                branch_name = String.replace(ref, "heads/", "")

                Logger.info(
                  "Updating reference refs/heads/#{branch_name} to point to commit #{commit_sha}"
                )

                case github_api.update_reference(
                       client,
                       owner,
                       repo,
                       "heads/#{branch_name}",
                       commit_sha,
                       false
                     ) do
                  {200, _, _} ->
                    Logger.info(
                      "Successfully updated #{length(file_updates)} files in #{owner}/#{repo} with a single commit"
                    )

                    {:ok, commit_sha}

                  error ->
                    Logger.error("Failed to update reference: #{inspect(error)}")
                    {:error, :reference_update_failed}
                end

              error ->
                Logger.error("Failed to create commit: #{inspect(error)}")
                {:error, :commit_creation_failed}
            end

          error ->
            Logger.error("Failed to create tree: #{inspect(error)}")
            {:error, :tree_creation_failed}
        end

      {:error, reason} ->
        Logger.error("Failed to get default branch reference: #{inspect(reason)}")
        {:error, :reference_retrieval_failed}
    end
  end

  @doc """
  Gets the default branch reference and its SHA.
  """
  def get_default_branch_ref(github_api, client, owner, repo) do
    case github_api.get_repo(client, owner, repo) do
      {200, repo_data, _} ->
        default_branch = repo_data["default_branch"]
        Logger.info("Default branch for #{owner}/#{repo} is #{default_branch}")

        case github_api.get_ref(client, owner, repo, "heads/#{default_branch}") do
          {200, ref_data, _} ->
            ref_sha = ref_data["object"]["sha"]
            {:ok, "heads/#{default_branch}", ref_sha}

          error ->
            Logger.error("Failed to get reference: #{inspect(error)}")
            {:error, :reference_get_failed}
        end

      error ->
        Logger.error("Failed to get repo details: #{inspect(error)}")
        {:error, :repo_get_failed}
    end
  end
end
